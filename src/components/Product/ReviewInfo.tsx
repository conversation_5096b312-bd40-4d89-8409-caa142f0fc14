import React, { use, useEffect, useState } from 'react';
import {
  View,
  Image,
  Text,
  TouchableOpacity,
  StyleSheet,
  Pressable,
} from 'react-native';
import { FlatList, ScrollView } from 'react-native-gesture-handler';
import { Rating, Winicon } from 'wini-mobile-components';
import { TypeMenuReview } from '../../Config/Contanst';
import { TypoSkin } from '../../assets/skin/typography';
import Config<PERSON><PERSON> from '../../Config/ConfigAPI';

const ReviewInfo = ({ item, index }: { item: any; index: number }) => {
  return (
    <Pressable style={styles.wrapper} key={`key ${index}`}>
      <View style={styles.review}>
        {item.Customer ?
          <Image
            source={{
              uri: ConfigAPI.urlImg + item?.Customer?.AvatarUrl,
            }}
            style={styles.avatar}
          />
          :
          <Image
            source={{
              uri: "data:image/jpeg;base64,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"
            }}
            style={styles.avatar}
          />
        }

        <View style={styles.reviewContent}>
          <Text style={styles.name}>{item?.Customer?.Name}</Text>
          <Text style={styles.rating}>
            <Rating value={item.Value} size={20} />
          </Text>
        </View>
      </View>
    </Pressable>
  );
};

const styles = StyleSheet.create({
  wrapper: {
  },

  review: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  avatar: {
    width: 50,
    height: 50,
    borderRadius: 50,
    marginRight: 16,
  },
  reviewContent: {
    flex: 1,
  },
  name: {
    ...TypoSkin.title2,
    fontWeight: '600',
  },
  rating: {
    color: '#FFD700', // Màu vàng cho sao
  },
  description: {
    fontSize: 14,
    marginVertical: 5,
  },
  imagesProduct: {
    flexDirection: 'row',
    marginTop: 10,
    width: '100%',
    height: 100,
  },
  images: {
    flexDirection: 'row',
    marginTop: 10,
  },
  productImage: {
    width: 100,
    height: 100,
    marginRight: 10,
    borderRadius: 5,
  },
  reviewDetail: {
    flexDirection: 'row',
    alignContent: 'center',
  },
  avatarProduct: {
    width: 80,
    height: 80,
    borderRadius: 20,
    marginRight: 10,
  },
});

export default ReviewInfo;
