import React, { use, useEffect, useState } from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  StyleSheet,
  Pressable,
  Image,
  ScrollView,
} from 'react-native';
import { ListTile, Winicon } from 'wini-mobile-components';
import ConfigAPI from '../../../Config/ConfigAPI';
import { ColorThemes } from '../../../assets/skin/colors';
import { Radio, RadioAction } from '../../Field/Radio';
import { FontAwesomeIcon } from '@fortawesome/react-native-fontawesome';
import { faAngleRight } from '@fortawesome/free-solid-svg-icons';

const ListItemCard = (
  { item, index }: { item: any; index: number },
  isSelected: string,
  handleSelect: any,
  selectItemChild: any,
  setSelectItemChild: any,
) => {
  {
    return (
      <TouchableOpacity
        style={styles.itemContainer}
        key={item?.Id}
        onPress={() => {
          if (selectItemChild && selectItemChild?.Id) {
            handleSelect(item);
          } else {
            setSelectItemChild(item);
          }
        }}
      >
        <View style={styles.itemText}>
          <Image
            source={{
              uri: ConfigAPI.urlImg + item.Img,
            }}
            style={{ width: 32, height: 32, borderRadius: 100 }}
          />
          <Text
            style={
              selectItemChild
                ? { marginLeft: 10, fontSize: 20 }
                : { marginLeft: 10, fontSize: 20, fontFamily: 'roboto' }
            }>
            {item?.Name}
          </Text>
        </View>
        {selectItemChild && selectItemChild?.Id ? (
          <View>
            {isSelected == item.Id ? <RadioAction /> : <Radio />}
          </View>
        ) : (
          <View>
            <FontAwesomeIcon
              icon={faAngleRight}
              color={ColorThemes.light.black}
              size={16}
            />
          </View>
        )}
      </TouchableOpacity>
    );
  }
};

const styles = StyleSheet.create({
  itemContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    borderBottomWidth: 1,
    borderBottomColor: '#eee',
    paddingLeft: 10,
    paddingRight: 20,
    paddingTop: 10,
    paddingBottom: 10,
  },
  itemText: {
    fontSize: 20,
    color: 'black',
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
  },
});

export default ListItemCard;
