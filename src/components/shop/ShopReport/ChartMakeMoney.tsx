import React, { useEffect, useState, useCallback } from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  StyleSheet,
  Dimensions,
} from 'react-native';
import { LineChart } from 'react-native-chart-kit';
import { ScrollView } from 'react-native-gesture-handler';
import { OrderDA } from '../../../modules/order/orderDA';
import { useSelectorShopState } from '../../../redux/hook/shopHook ';
import { StatusOrder } from '../../../Config/Contanst';
import dayjs from 'dayjs';
const weekOfYear = require('dayjs/plugin/weekOfYear');
const isoWeek = require('dayjs/plugin/isoWeek');
dayjs.extend(weekOfYear);
dayjs.extend(isoWeek);
import { FLoading } from 'wini-mobile-components';
import { Ultis } from '../../../utils/Utils';

// Hàm format số tiền với dấu ph<PERSON>y
const ChartMoney = () => {
  const [dateRange] = useState('29 May 2025'); // Cập nhật ngày hiện tại
  const [selectedTime, setSelectedTime] = useState('1H');
  const [analysisData, setAnalysisData] = useState<number[]>([]);
  const [labels, setLabels] = useState<string[]>([]);
  const [money, setMoney] = useState<number>(0);
  const [getdataAnalysis, setGetdataAnalysis] = useState<any[]>([]);
  const [titleGetdataAnalysis, setTitleGetdataAnalysis] = useState<string>('')

  const [isLoading, setIsLoading] = useState(false);
  const orderDA = new OrderDA();
  const shopInfo = useSelectorShopState().data;

  const timeOptions = ['1H', '1D', '1W', '1M', '6M', '1Y'];
  const currentHour = dayjs().format('HH');
  const currentDay = dayjs().format('dddd');

  const callApiGetData = async () => {
    try {
      // Kiểm tra shopInfo có tồn tại và có dữ liệu không
      if (!shopInfo || !Array.isArray(shopInfo) || shopInfo.length === 0) {
        console.warn('ShopInfo is not available or empty');
        setGetdataAnalysis([]);
        return;
      }

      // Kiểm tra shopInfo[0] có Id không
      if (!shopInfo[0] || !shopInfo[0].Id) {
        console.warn('Shop ID is not available');
        setGetdataAnalysis([]);
        return;
      }

      const response = await orderDA.getOrderByShopId(
        shopInfo[0].Id,
        StatusOrder.success,
      );

      console.log('check-callApiGetData', response);

      if (response?.code === 200 && response?.data) {
        setGetdataAnalysis(response.data);
      } else {
        console.warn('API response is not successful:', response);
        setGetdataAnalysis([]);
      }
    } catch (error) {
      console.error('Error in callApiGetData:', error);
      setGetdataAnalysis([]);
    }
  }


  let getanalysisDataHour = useCallback(async () => {
    try {
      setIsLoading(true);
      console.log("getdataAnalysis", getdataAnalysis)
      if (getdataAnalysis.length > 0) {
        console.log(
          'check-res-42',
          dayjs(getdataAnalysis[0]?.DateCreated).format('HH'),
        );
        let dataHours = getdataAnalysis.filter((item: any) => {
          if (!item?.DateCreated) return false;
          return (
            dayjs(item.DateCreated).format('HH') == currentHour &&
            dayjs(item.DateCreated).format('dddd') == currentDay
          );
        });
        console.log('check-dataHours', dataHours);
        let MoneyHour = [
          0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
          0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
          0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
        ];
        for (let item of dataHours) {
          if (item?.DateCreated && item?.Quantity && item?.Price) {
            // Get the minute from the timestamp
            const minute = Number(dayjs(item.DateCreated).format('mm'));
            // Calculate the index based on 5-minute intervals (0-4, 5-9, etc.)
            if (minute >= 0 && minute < MoneyHour.length) {
              MoneyHour[minute] += Number(item?.Total) || 0;
            }
          }
        }

        // Tính tổng tiền
        const totalMoney = MoneyHour.reduce((total, num) => total + num, 0);
        setMoney(totalMoney);
        setAnalysisData(MoneyHour);
      } else {
        console.error('Failed to fetch order data:', getdataAnalysis);
        setAnalysisData([0]);
        setMoney(0);
      }
    } catch (error) {
      console.error('Error in getanalysisDataHour:', error);
      setAnalysisData([0]);
      setMoney(0);
    } finally {
      setIsLoading(false);
    }
  }, [getdataAnalysis, currentHour, currentDay]);
  let getanalysisDataDay = useCallback(async () => {
    try {
      setIsLoading(true);
      if (getdataAnalysis.length > 0) {
        // Lọc dữ liệu cho ngày hiện tại
        let dataDay = getdataAnalysis.filter((item: any) => {
          if (!item?.DateCreated) return false;
          return dayjs(item.DateCreated).format('dddd') === currentDay;
        });
        // Khởi tạo mảng 24 giờ với giá trị 0
        let MoneyDay = new Array(24).fill(0);
        for (let item of dataDay) {
          if (item?.DateCreated && item?.Quantity && item?.Price) {
            let hour = Number(dayjs(item.DateCreated).format('HH'));
            // Kiểm tra hour có hợp lệ không (0-23)
            if (hour >= 0 && hour < 24) {
              MoneyDay[hour] += Number(item?.Total) || 0;
            }
          }
        }

        console.log('check-MoneyDay', MoneyDay);

        // Tính tổng tiền
        const totalMoney = MoneyDay.reduce((total, num) => total + num, 0);
        setMoney(totalMoney);
        setAnalysisData(MoneyDay);
      } else {
        console.error('Failed to fetch order data:', getdataAnalysis);
        setAnalysisData([0]);
        setMoney(0);
      }
    } catch (error) {
      console.error('Error in getanalysisDataDay:', error);
      setAnalysisData([0]);
      setMoney(0);
    } finally {
      setIsLoading(false);
    }
  }, [getdataAnalysis, currentDay]);
  let getanalysisDateWeek = useCallback(async () => {
    try {
      setIsLoading(true);

      const currentDate = dayjs();
      const startOfWeek = currentDate.startOf('week');
      const endOfWeek = currentDate.endOf('week');

      if (getdataAnalysis.length > 0) {
        // Initialize array with 7 zeros for each day of the week
        let MoneyDay = [0, 0, 0, 0, 0, 0, 0];

        let dataDay = getdataAnalysis.filter((item: any) => {
          if (!item?.DateCreated) return false;
          const itemDate = dayjs(item.DateCreated);
          // Sử dụng logic để bao gồm cả ngày đầu và cuối tuần
          return (
            (itemDate.isAfter(startOfWeek.subtract(1, 'day')) ||
              itemDate.isSame(startOfWeek, 'day')) &&
            (itemDate.isBefore(endOfWeek.add(1, 'day')) ||
              itemDate.isSame(endOfWeek, 'day'))
          );
        });
        for (let item of dataDay) {
          if (item?.DateCreated && item?.Total) {
            // Get the day of week (0-6, where 0 is Sunday)
            const dayOfWeek = dayjs(item.DateCreated).day();
            console.log('check-dayOfWeek', dayOfWeek, 'item date:', dayjs(item.DateCreated).format('YYYY-MM-DD'));

            // Adjust index to match Vietnamese day order (Monday = 0, Sunday = 6)
            const adjustedIndex = dayOfWeek === 0 ? 6 : dayOfWeek - 1;

            if (adjustedIndex >= 0 && adjustedIndex < 7) {
              MoneyDay[adjustedIndex] += Number(item?.Total) || 0;
            }
          }
        }


        // Calculate total money for the week
        const totalMoney = MoneyDay.reduce((total, num) => total + num, 0);
        setMoney(totalMoney);
        setAnalysisData(MoneyDay);
      } else {
        console.error('Failed to fetch order data:', getdataAnalysis);
        setAnalysisData([0]);
        setMoney(0);
      }
    } catch (error) {
      console.error('Error in getanalysisDateWeek:', error);
      setAnalysisData([0]);
      setMoney(0);
    } finally {
      setIsLoading(false);
    }
  }, [getdataAnalysis]);
  let getanalysisDateMonth = useCallback(async () => {
    try {
      setIsLoading(true);

      const currentDate = dayjs();
      const startOfMonth = currentDate.startOf('month');
      const endOfMonth = currentDate.endOf('month');
      const daysInMonth = endOfMonth.date(); // Get number of days in current month

      if (getdataAnalysis.length > 0) {
        let dataDay = getdataAnalysis.filter((item: any) => {
          if (!item?.DateCreated) return false;
          const itemDate = dayjs(item.DateCreated);
          // Sử dụng logic để bao gồm cả ngày đầu và cuối tháng
          return (
            (itemDate.isAfter(startOfMonth.subtract(1, 'day')) ||
              itemDate.isSame(startOfMonth, 'day')) &&
            (itemDate.isBefore(endOfMonth.add(1, 'day')) ||
              itemDate.isSame(endOfMonth, 'day'))
          );
        });

        // Initialize array with zeros for each day of the month
        let MoneyMonth = new Array(daysInMonth).fill(0);

        for (let item of dataDay) {
          if (item?.DateCreated && item?.Quantity && item?.Price) {
            const dayOfMonth = dayjs(item.DateCreated).date() - 1; // Get day of month (0-based index)
            // Kiểm tra dayOfMonth có hợp lệ không (0 đến daysInMonth-1)
            if (dayOfMonth >= 0 && dayOfMonth < daysInMonth) {
              MoneyMonth[dayOfMonth] += Number(item?.Total) || 0;
            }
          }
        }

        // Tính tổng tiền
        const totalMoney = MoneyMonth.reduce((total, num) => total + num, 0);
        setMoney(totalMoney);
        setAnalysisData(MoneyMonth);
      } else {
        console.error('Failed to fetch order data:', getdataAnalysis);
        setAnalysisData([0]);
        setMoney(0);
      }
    } catch (error) {
      console.error('Error in getanalysisDateMonth:', error);
      setAnalysisData([0]);
      setMoney(0);
    } finally {
      setIsLoading(false);
    }
  }, [getdataAnalysis]);
  let getanalysisDateSixMonth = useCallback(
    async (data: any[]) => {
      try {
        setIsLoading(true);



        if (getdataAnalysis.length > 0) {
          // Lọc dữ liệu cho 6 tháng gần nhất
          let dataDay = getdataAnalysis.filter((item: any) => {
            if (!item?.DateCreated) return false;
            const itemDate = dayjs(item.DateCreated);
            const startDate = dayjs(data[0].startOfMonth);
            const endDate = dayjs(data[data.length - 1].endOfMonth);

            return (
              itemDate.isAfter(startDate.subtract(1, 'day')) &&
              itemDate.isBefore(endDate.add(1, 'day'))
            );
          });

          // Initialize array with 6 zeros for each month
          let MoneyMonth = new Array(6).fill(0);

          for (let item of dataDay) {
            if (item?.DateCreated && item?.Quantity && item?.Price) {
              const itemDate = dayjs(item.DateCreated);
              const currentDate = dayjs();

              // Tính toán index tháng (0-5) dựa trên khoảng cách từ tháng hiện tại
              const monthDiff = currentDate.diff(itemDate, 'month');
              const monthIndex = 5 - monthDiff;

              // Kiểm tra monthIndex có hợp lệ không (0-5)
              if (monthIndex >= 0 && monthIndex < 6) {
                MoneyMonth[monthIndex] += Number(item?.Total) || 0;
              }
            }
          }

          // Tính tổng tiền
          const totalMoney = MoneyMonth.reduce((total, num) => total + num, 0);
          setMoney(totalMoney);
          setAnalysisData(MoneyMonth);
        } else {
          console.error('Failed to fetch order data:', getdataAnalysis);
          setAnalysisData([0]);
          setMoney(0);
        }
      } catch (error) {
        console.error('Error in getanalysisDateSixMonth:', error);
        setAnalysisData([0]);
        setMoney(0);
      } finally {
        setIsLoading(false);
      }
    },
    [getdataAnalysis],
  );
  let getanalysisDateOneYear = useCallback(
    async (data: any) => {
      try {
        setIsLoading(true);



        if (getdataAnalysis.length > 0) {
          let dataDay = getdataAnalysis.filter((item: any) => {
            if (!item?.DateCreated) return false;
            const itemDate = dayjs(item.DateCreated);
            const startDate = dayjs(data.firstDay);
            const endDate = dayjs(data.lastDay);

            return (
              itemDate.isAfter(startDate.subtract(1, 'day')) &&
              itemDate.isBefore(endDate.add(1, 'day'))
            );
          });

          // Initialize array with 12 zeros for each month
          let MoneyMonth = new Array(12).fill(0);

          for (let item of dataDay) {
            if (item?.DateCreated && item?.Quantity && item?.Price) {
              const monthIndex = dayjs(item.DateCreated).month(); // Get month index (0-11)
              // Kiểm tra monthIndex có hợp lệ không (0-11)
              if (monthIndex >= 0 && monthIndex < 12) {
                MoneyMonth[monthIndex] += Number(item?.Total) || 0;
              }
            }
          }

          // Tính tổng tiền
          const totalMoney = MoneyMonth.reduce((total, num) => total + num, 0);
          setMoney(totalMoney);
          setAnalysisData(MoneyMonth);
        } else {
          console.error('Failed to fetch order data:', getdataAnalysis);
          setAnalysisData([0]);
          setMoney(0);
        }
      } catch (error) {
        console.error('Error in getanalysisDateOneYear:', error);
        setAnalysisData([0]);
        setMoney(0);
      } finally {
        setIsLoading(false);
      }
    },
    [getdataAnalysis],
  );
  let dataArray = [0];
  const chartDataH = {
    labels: labels,
    datasets: [
      {
        data: analysisData.length > 0 ? analysisData : dataArray,
      },
    ],
  };

  useEffect(() => {
    setLabels([
      `${currentHour}:00`,
      `${currentHour}:05`,
      `${currentHour}:10`,
      `${currentHour}:15`,
      `${currentHour}:20`,
      `${currentHour}:25`,
      `${currentHour}:30`,
      `${currentHour}:35`,
      `${currentHour}:40`,
      `${currentHour}:45`,
      `${currentHour}:50`,
      `${currentHour}:55`,
    ]);
    getanalysisDataHour();
  }, [getanalysisDataHour]);
  useEffect(() => {

    if (selectedTime == '1H') {
      getanalysisDataHour();
      setTitleGetdataAnalysis('Doanh thu giờ này')
      setLabels([
        `${currentHour}:00`,
        `${currentHour}:05`,
        `${currentHour}:10`,
        `${currentHour}:15`,
        `${currentHour}:20`,
        `${currentHour}:25`,
        `${currentHour}:30`,
        `${currentHour}:35`,
        `${currentHour}:40`,
        `${currentHour}:45`,
        `${currentHour}:50`,
        `${currentHour}:55`,
      ]);
    }
    if (selectedTime == '1D') {
      getanalysisDataDay();
      setTitleGetdataAnalysis('Doanh thu ngày hôm nay')
      setLabels([
        '00:00',
        '01:00',
        '02:00',
        '03:00',
        '04:00',
        '05:00',
        '06:00',
        '07:00',
        '08:00',
        '09:00',
        '10:00',
        '11:00',
        '12:00',
        '13:00',
        '14:00',
        '15:00',
        '16:00',
        '17:00',
        '18:00',
        '19:00',
        '20:00',
        '21:00',
        '22:00',
        '23:00',
      ]);
    }
    if (selectedTime == '1W') {
      getanalysisDateWeek();
      setTitleGetdataAnalysis('Doanh thu tuần này')
      setLabels([
        'Thứ hai',
        'Thứ ba',
        'Thứ tư',
        'Thứ năm',
        'Thứ sáu',
        'Thứ bảy',
        'Chủ nhật',
      ]);
    }
    if (selectedTime == '1M') {
      getanalysisDateMonth();
      setTitleGetdataAnalysis('Doanh thu tháng này')
      const year = dayjs().year();
      const month = dayjs().month();
      const allDays = [];
      const lastDay = new Date(year, month + 1, 0); // Ngày 0 của tháng tiếp theo = ngày cuối tháng hiện tại
      const daysInMonth = lastDay.getDate(); // Số ngày trong tháng
      for (let day = 1; day <= daysInMonth; day++) {
        const date = new Date(year, month, day);
        const formattedDate = `${String(date.getDate()).padStart(
          2,
          '0',
        )}/${String(date.getMonth() + 1).padStart(
          2,
          '0',
        )}/${date.getFullYear()}`;
        allDays.push(formattedDate);
      }
      setLabels(allDays);
    }
    if (selectedTime == '6M') {
      setTitleGetdataAnalysis('Doanh thu 6 tháng gần nhất')
      // Lấy ngày hiện tại
      const currentDate = dayjs(); // 2025-06-10
      // Lấy tháng hiện tại
      const currentMonth = currentDate.month(); // Tháng 6 (Day.js đếm từ 0, nên là 5)
      const currentYear = currentDate.year(); // 2025
      // Tạo mảng để lưu thông tin 6 tháng gần nhất
      const recentMonths = [];
      // Lặp để lấy 6 tháng trước đó, bao gồm tháng hiện tại
      for (let i = 5; i >= 0; i--) {
        const pastDate = currentDate.subtract(i, 'month');
        const monthInfo = {
          month: pastDate.month() + 1, // Cộng 1 vì Day.js đếm tháng từ 0
          year: pastDate.year(),
          startOfMonth: pastDate.startOf('month').format('YYYY-MM-DD'),
          endOfMonth: pastDate.endOf('month').format('YYYY-MM-DD'),
        };
        recentMonths.push(monthInfo);
      }

      // In thông tin 6 tháng gần nhất
      // console.log('Thông tin 6 tháng gần nhất:');
      let dataMounth: any[] = [];
      recentMonths.forEach((month, index) => {
        console.log(`${month.month}/${month.year}:`);
        dataMounth.push({
          startOfMonth: month.startOfMonth,
          endOfMonth: month.endOfMonth,
        });
      });
      getanalysisDateSixMonth(dataMounth);
      setLabels(recentMonths.map(month => `${month.month}/${month.year}`));
    }
    if (selectedTime == '1Y') {
      setTitleGetdataAnalysis('Doanh thu năm nay')
      const currentDate = dayjs(); // 2025-06-10

      // Lấy năm hiện tại
      const currentYear = currentDate.year();
      // Tạo mảng để lưu thông tin các tháng
      const monthsInYear = [];

      // Lặp qua 12 tháng (Day.js đếm tháng từ 0 đến 11)
      for (let i = 0; i < 12; i++) {
        const date = dayjs(`${currentYear}-${i + 1}-01`); // Tạo ngày đầu của mỗi tháng
        const monthInfo = {
          month: i + 1, // Số tháng (cộng 1 vì Day.js đếm từ 0)
          monthName: date.format('MMMM'), // Tên tháng (bằng tiếng Anh)
          startOfMonth: date.startOf('month').format('YYYY-MM-DD'),
          endOfMonth: date.endOf('month').format('YYYY-MM-DD'),
        };
        monthsInYear.push(monthInfo);
      }
      console.log('check-monthsInYear', monthsInYear);
      getanalysisDateOneYear({
        firstDay: monthsInYear[0].startOfMonth,
        lastDay: monthsInYear[monthsInYear.length - 1].endOfMonth,
      });
      setLabels(monthsInYear.map(month => `${month.month}`));
    }
  }, [

    selectedTime,
    getanalysisDataHour,
    getanalysisDataDay,
    getanalysisDateWeek,
    getanalysisDateMonth,
    getanalysisDateSixMonth,
    getanalysisDateOneYear,
    currentHour,
  ]);

  useEffect(() => {
    // Chỉ gọi API khi shopInfo có sẵn
    if (shopInfo && Array.isArray(shopInfo) && shopInfo.length > 0 && shopInfo[0]?.Id) {
      callApiGetData();
    }
  }, [shopInfo]);


  return (
    <View style={styles.container}>
      <FLoading visible={isLoading} />
      <Text style={styles.title}>{titleGetdataAnalysis}</Text>
      <Text style={styles.revenue}>{Ultis.money(money)} VNĐ</Text>
      <Text style={styles.dateRange}>{dayjs().format('DD/MM/YYYY HH:mm')}</Text>
      <ScrollView style={styles.chartContainer} horizontal={true}>
        <LineChart
          data={chartDataH}
          width={selectedTime == '1M' ? 2400 : 1600}
          height={300}
          chartConfig={{
            backgroundColor: '#fff',
            backgroundGradientFrom: '#fff',
            backgroundGradientTo: '#fff',
            decimalPlaces: 0,
            color: (opacity = 1) => `rgba(0, 123, 255, ${opacity})`,
            labelColor: (opacity = 1) => `rgba(0, 0, 0, ${opacity})`,
            style: {
              borderRadius: 8,
            },
            propsForDots: {
              r: '6',
              strokeWidth: '2',
              stroke: '#007bff',
            },
            fillShadowGradient: '#e6f0fa',
            fillShadowGradientOpacity: 0.6,
            count: 5,
          }}
          bezier
          style={{
            marginVertical: 8,
            borderRadius: 8,
          }}
          withInnerLines={false}
          yAxisLabel=""
          yAxisSuffix=""
          formatYLabel={(value) => {
            const numValue = parseFloat(value);
            if (numValue >= 1000000) {
              return `${(numValue / 1000000).toFixed(2)} M`;
            } else if (numValue >= 1000) {
              return `${(numValue / 1000).toFixed(2)} K`;
            }
            return numValue.toString();
          }}
        />
      </ScrollView>
      <View style={styles.timeOptions}>
        {timeOptions.map(option => (
          <TouchableOpacity
            key={option}
            style={[
              styles.timeButton,
              selectedTime === option && styles.selectedTime,
            ]}
            onPress={() => setSelectedTime(option)}>
            <Text
              style={[
                styles.timeText,
                selectedTime === option ? styles.selectedTimeText : styles.timeText
              ]}>
              {option}
            </Text>
          </TouchableOpacity>
        ))}
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    padding: 16,
    backgroundColor: '#fff',
    borderRadius: 8,
  },
  tabContainer: {
    flexDirection: 'row',
    marginBottom: 10,
  },
  tab: {
    padding: 4,
    borderBottomWidth: 2,
    borderBottomColor: 'transparent',
    marginLeft: 5,
    marginRight: 5,
  },
  tabAction: {
    padding: 4,
    borderBottomWidth: 2,
    borderBottomColor: 'transparent',
    marginLeft: 5,
    marginRight: 5,
    backgroundColor: '#007bff',
    borderRadius: 10,
  },
  tabText: {
    fontSize: 20,
    color: 'blue',
    padding: 3,
  },
  title: {
    fontSize: 16,
    fontWeight: 'bold',
    marginBottom: 8,
  },
  revenue: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#007bff',
    marginBottom: 8,
  },
  dateRange: {
    fontSize: 14,
    color: '#007bff',
    marginBottom: 16,
  },
  chartContainer: {
    marginBottom: 16,
  },
  timeOptions: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  timeButton: {
    padding: 8,
    borderRadius: 4,
  },
  selectedTime: {
    backgroundColor: '#5264BE',
  },
  timeText: {
    color: '#007bff',
    fontWeight: 'bold',
  },
  selectedTimeText: {
    color: '#fff',
    width: 20,
    textAlign: 'center',
    fontWeight: 'bold',
    borderRadius: 20

  },
});

export default ChartMoney;
