import React from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  FlatList,
  Dimensions,
} from 'react-native';
import {Winicon} from 'wini-mobile-components';
import ProductCard from '../modules/Product/card/ProductCard';
import { Product } from '../redux/models/product';

const {width} = Dimensions.get('window');

// Kích thước của mỗi item sản phẩm
const ITEM_WIDTH = width * 0.45;
const ITEM_HEIGHT = ITEM_WIDTH * 2; // Tăng chiều cao một chút
const ITEM_SPACING = 12;

// Tạo component ItemSeparator bên ngoài component chính
const ItemSeparator = React.memo(() => <View style={{width: ITEM_SPACING}} />);

interface ProductCarouselProps {
  title: string;
  products: Product[];
  onSeeAll?: () => void;
  onProductPress?: (product: Product) => void;
  onAddToCart?: (product: Product) => void;
  onFavoritePress?: (product: Product) => void;
}

const ProductCarousel: React.FC<ProductCarouselProps> = ({
  title,
  products,
  onSeeAll,
  onProductPress,
  onAddToCart,
  onFavoritePress,
}) => {
  const renderItem = ({item}: {item: Product}) => {
    return (
      <ProductCard
        key={item.Id}
        item={item}
        onPress={onProductPress}
        onAddToCart={onAddToCart}
        onFavoritePress={onFavoritePress}
        width={ITEM_WIDTH}
        height={ITEM_HEIGHT}
      />
    );
  };

  return (
    <View style={styles.container}>
      <View style={styles.header}>
        <Text style={styles.sectionTitle}>{title}</Text>
        <TouchableOpacity onPress={onSeeAll} activeOpacity={0.7}>
          <View style={styles.seeAllButton}>
            <Text style={styles.seeAllText}>Xem thêm</Text>
            <Winicon
              src="outline/arrows/circle-arrow-right"
              size={16}
              color="#4169E1"
            />
          </View>
        </TouchableOpacity>
      </View>

      <FlatList
        data={products}
        renderItem={renderItem}
        keyExtractor={item => item.Id?.toString()}
        horizontal
        showsHorizontalScrollIndicator={false}
        contentContainerStyle={styles.listContent}
        snapToInterval={ITEM_WIDTH + ITEM_SPACING}
        snapToAlignment="start"
        decelerationRate="fast"
        pagingEnabled={false}
        disableIntervalMomentum={true}
        ItemSeparatorComponent={ItemSeparator}
      />
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    // marginVertical: 16,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 16,
    marginBottom: 12,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#000',
  },
  seeAllButton: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  seeAllText: {
    fontSize: 14,
    color: '#4169E1',
    marginRight: 4,
    fontWeight: '500',
  },
  listContent: {
    paddingHorizontal: 16,
  },
});

export default ProductCarousel;
