import {ImageBackground, Text, View, Animated} from 'react-native';
import LinearGradient from 'react-native-linear-gradient';
import {AppSvg} from 'wini-mobile-components';
import iconSvg from '../../svg/icon';
import {TypoSkin} from '../../assets/skin/typography';
import {ColorThemes} from '../../assets/skin/colors';
import {useSelectorCustomerState} from '../../redux/hook/customerHook';
import {useEffect, useState, useRef} from 'react';
import {DataController} from '../../base/baseController';
import {
  TransactionType,
  TransactionStatus,
  StatusOrder,
} from '../../Config/Contanst';
import ConfigAPI from '../../Config/ConfigAPI';
import FastImage from 'react-native-fast-image';
import {TouchableOpacity} from 'react-native';
import {RootScreen} from '../../router/router';
import {useNavigation} from '@react-navigation/native';
import {Ultis} from '../../utils/Utils';

// Skeleton Component
const SkeletonBox = ({
  width,
  height,
  style,
}: {
  width: number | string;
  height: number;
  style?: any;
}) => {
  const animatedValue = useRef(new Animated.Value(0)).current;

  useEffect(() => {
    const animation = Animated.loop(
      Animated.sequence([
        Animated.timing(animatedValue, {
          toValue: 1,
          duration: 1000,
          useNativeDriver: false,
        }),
        Animated.timing(animatedValue, {
          toValue: 0,
          duration: 1000,
          useNativeDriver: false,
        }),
      ]),
    );
    animation.start();
    return () => animation.stop();
  }, []);

  const opacity = animatedValue.interpolate({
    inputRange: [0, 1],
    outputRange: [0.3, 0.7],
  });

  return (
    <Animated.View
      style={[
        {
          width,
          height,
          backgroundColor: '#ccc',
          borderRadius: 4,
          opacity,
        },
        style,
      ]}
    />
  );
};

const PointHome = () => {
  const customer = useSelectorCustomerState().data;
  const [totalReward, setTotalReward] = useState(0);
  const [currentRank, setCurrentRank] = useState<any>(null);
  const [loading, setLoading] = useState(false);
  const [orderCount, setOrderCount] = useState(0);
  const [referralCount, setReferralCount] = useState(0);
  const navigation = useNavigation<any>();
  const fetchData = async () => {
    if (!customer?.Id) return;

    setLoading(true);

    try {
      // Gọi tất cả API cùng lúc với Promise.all
      const [totalRewardRes, resSum, resRank] = await Promise.all([
        // 1. Tính tổng điểm tất cả
        new DataController('HistoryReward').group({
          reducers:
            'LOAD * GROUPBY 1 @CustomerId REDUCE SUM 1 @Value AS TotalReward',
          searchRaw: `@CustomerId: {${customer.Id}} ((@Status: [${TransactionStatus.success}] @Value: [0 +inf]) | (@Status: [${TransactionStatus.pending} ${TransactionStatus.success}] @Value: [-inf 0]))`,
        }),

        // 2. Tính tổng điểm từ hoa hồng và nhiệm vụ
        new DataController('HistoryReward').group({
          reducers:
            'LOAD * GROUPBY 1 @CustomerId REDUCE SUM 1 @Value AS TotalReward',
          searchRaw: `@CustomerId: {${customer.Id}} (@Type: [${TransactionType.hoahong}] | @Type: [${TransactionType.mission}]) @Status: [${TransactionStatus.success}]`,
        }),

        // 3. Đếm số đơn hàng thành công
        // new DataController('Order').group({
        //     reducers: 'LOAD * GROUPBY 1 @CustomerId REDUCE COUNT 1 @Id AS OrderCount',
        //     searchRaw: `@CustomerId: {${customer.Id}} @Status: [${StatusOrder.success}]`,
        // }),

        // // 4. Đếm số người được mời (referral)
        // new DataController('Customer').group({
        //     reducers: 'LOAD * GROUPBY 1 @ParentId REDUCE COUNT 1 @Id AS ReferralCount',
        //     searchRaw: `@ParentId: {${customer.Id}}`,
        // }),

        // 5. Lấy thông tin config rank
        new DataController('ConfigRank').getAll(),
      ]);

      // Xử lý kết quả tổng điểm hiển thị
      if (totalRewardRes.code === 200 && totalRewardRes.data.length > 0) {
        setTotalReward(totalRewardRes.data[0].TotalReward || 0);
      }

      // Xử lý kết quả điểm để tính rank
      let totalScore = 0;
      if (resSum.code === 200 && resSum.data.length > 0) {
        totalScore = resSum.data[0].TotalReward || 0;
      }

      // Xử lý kết quả đơn hàng
      // const successfulOrders = resOrder.code === 200 && resOrder.data.length > 0 ? resOrder.data[0].OrderCount || 0 : 0;
      // setOrderCount(successfulOrders);

      // Xử lý kết quả referral
      // const referrals = resReferral.code === 200 && resReferral.data.length > 0 ? resReferral.data[0].ReferralCount || 0 : 0;
      // setReferralCount(referrals);

      // Xử lý rank và xác định rank hiện tại
      if (resRank.code === 200 && resRank.data.length > 0) {
        const ranksData = resRank.data;

        // Sắp xếp ranks theo điểm số tăng dần
        const sortedRanks = [...ranksData].sort(
          (a, b) => parseFloat(a.Score) - parseFloat(b.Score),
        );

        // Tìm rank hiện tại dựa trên điều kiện
        let achievedRank = null;

        for (const rank of sortedRanks) {
          const requiredScore = parseFloat(rank.Score);

          // Kiểm tra điều kiện điểm số
          if (totalScore >= requiredScore) {
            // Kiểm tra điều kiện đơn hàng (nếu có)
            // const orderCondition = rank.TotalOrder || 0;
            // if (successfulOrders >= orderCondition) {
            //     // Kiểm tra điều kiện referral (nếu có)
            //     const referralCondition = rank.RefCount || 0;
            //     if (referrals >= referralCondition) {

            //     }
            // }
            achievedRank = rank;
          }
        }

        setCurrentRank(achievedRank);
      }
    } catch (error) {
      console.error('Error fetching data:', error);
    } finally {
      setLoading(false);
    }
  };
  useEffect(() => {
    if (customer?.Id) {
      fetchData();
    }
  }, [customer?.Id]);
  return (
    <View
      style={{
        height: 82,
        backgroundColor: 'white',
        marginHorizontal: 16,
        marginTop: 16,
        position: 'relative',
        borderRadius: 10,
        overflow: 'hidden',
      }}>
      {loading ? (
        <SkeletonBox width={'100%'} height={82} />
      ) : (
        <LinearGradient
          colors={['#90C8FB', '#8DC4F7E5', '#B6F5FE']}
          start={{x: 0, y: 0.5}}
          end={{x: 1, y: 0.5}}
          style={{
            borderRadius: 10,
            height: '100%',
            flex: 1,
            overflow: 'hidden',
            width: '100%',
          }}>
          <ImageBackground
            source={require('../../assets/bg04.png')}
            resizeMode="contain"
            style={{
              position: 'absolute',
              bottom: -30,
              left: -20,
              width: 110,
              height: 110,
            }}
          />
          <View style={{paddingLeft: 76, paddingTop: 16}}>
            <View style={{flexDirection: 'row', alignItems: 'center', gap: 8}}>
              <AppSvg SvgSrc={iconSvg.moneyGold} size={20} />
              {loading ? (
                <SkeletonBox width={80} height={20} />
              ) : (
                <Text
                  style={{
                    ...TypoSkin.heading7,
                    color: ColorThemes.light.neutral_text_title_color,
                  }}>
                  {Ultis.money(totalReward)} điểm
                </Text>
              )}
            </View>
            <View style={{flexDirection: 'row', alignItems: 'center', gap: 8}}>
              {loading ? (
                <>
                  <SkeletonBox
                    width={20}
                    height={20}
                    style={{borderRadius: 10}}
                  />
                  <SkeletonBox width={100} height={20} />
                </>
              ) : currentRank ? (
                <>
                  {currentRank.Icon ? (
                    <FastImage
                      source={{uri: ConfigAPI.urlImg + currentRank.Icon}}
                      style={{width: 20, height: 20}}
                      resizeMode="contain"
                    />
                  ) : (
                    <AppSvg SvgSrc={iconSvg.ruby} size={20} />
                  )}
                  <Text
                    style={{
                      ...TypoSkin.heading7,
                      color: ColorThemes.light.neutral_text_title_color,
                    }}>
                    Hạng {currentRank.Name}
                  </Text>
                </>
              ) : (
                <>
                  <AppSvg SvgSrc={iconSvg.ruby} size={20} />
                  <Text
                    style={{
                      ...TypoSkin.heading7,
                      color: ColorThemes.light.neutral_text_title_color,
                      fontWeight: '500',
                    }}>
                    Chưa có hạng
                  </Text>
                </>
              )}
            </View>
          </View>

          <TouchableOpacity
            style={{position: 'absolute', right: 16, top: 22}}
            onPress={() => {
              navigation.push(RootScreen.GiftExchange);
            }}>
            <LinearGradient
              start={{x: 0, y: 0}}
              end={{x: 1, y: 0}}
              colors={['#FFC043', '#FFD275', '#FFE0A3', '#FFF3DF']}
              style={{borderRadius: 8, minHeight: 32}}>
              <Text
                style={{
                  ...TypoSkin.body2,
                  color: ColorThemes.light.primary_main_color,
                  fontSize: 14,
                  fontWeight: '500',
                  paddingHorizontal: 8,
                  paddingVertical: 4,
                }}>
                Đổi quà
              </Text>
            </LinearGradient>
          </TouchableOpacity>
        </LinearGradient>
      )}
    </View>
  );
};

export default PointHome;
