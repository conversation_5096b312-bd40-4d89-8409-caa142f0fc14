import {ActivityIndicator, StyleSheet, View} from 'react-native';
import HotProductsRow from '../HotProductsRow';
import {useNavigation} from '@react-navigation/native';
import {productAction} from '../../../redux/actions/productAction';
import {useEffect, useState} from 'react';
import {SquareProductItem} from '../../../components/SquareProductCard';
import {ColorThemes} from '../../../assets/skin/colors';

const FreeShipProductSection = () => {
  const navigation = useNavigation();
  const [products, setProducts] = useState<SquareProductItem[]>([]);
  const [loading, setLoading] = useState(false);

  useEffect(() => {
    initData();
  }, []);

  const initData = async () => {
    try {
      setLoading(true);
      const data = await productAction.find({
        page: 1,
        size: 10,
        query: '@IsFreeShip:true',
      });
      const products = data.map((item: any) => ({
        Id: item.Id,
        Name: item.Name,
        Price: item.Price,
        Img: item.Img,
        rating: item.Rating,
        soldCount: item.SoldCount,
        Description: item.Description,
      }));
      setProducts(products);
    } finally {
      setLoading(false);
    }
  };

  if (loading) {
    return (
      <View style={{flex: 1, justifyContent: 'center', alignItems: 'center'}}>
        <ActivityIndicator
          size="large"
          color={ColorThemes.light.primary_main_color}
        />
      </View>
    );
  }

  return (
    <HotProductsRow
      title="Free Ship"
      products={products}
      onSeeAll={() => {}}
      showRating={false}
    />
  );
};
const styles = StyleSheet.create({});

export default FreeShipProductSection;
