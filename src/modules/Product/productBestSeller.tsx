/* eslint-disable react-hooks/exhaustive-deps */
/* eslint-disable react/no-unstable-nested-components */
/* eslint-disable react-native/no-inline-styles */
import React, {useEffect, useRef, useState} from 'react';
import {View, Text, FlatList} from 'react-native';
import {AppButton, FDialog} from 'wini-mobile-components';
import {useTranslation} from 'react-i18next';
import {useNavigation} from '@react-navigation/native';
import {ProductDA} from './productDA';
import {ColorThemes} from '../../assets/skin/colors';
import {TypoSkin} from '../../assets/skin/typography';
import {RootScreen} from '../../router/router';
import ProductCarousel from '../../components/ProductCarousel';
import {AppDispatch} from '../../redux/store/store';
import {useDispatch} from 'react-redux';
import {CartActions} from '../../redux/reducers/CartReducer';

interface Props {
  horizontal?: boolean;
  titleList?: string;
  isSeeMore?: boolean;
  id: string;
  onPressSeeMore?: () => void;
  onRefresh?: () => void;
}

export default function ProductBestSeller(props: Props) {
  const [data, setData] = useState<Array<any>>([]);
  const [isLoading, setLoading] = useState(true);
  const {t} = useTranslation();
  const navigation = useNavigation<any>();
  const dialogRef = useRef<any>(null);
  const productDA = new ProductDA();
  const dispatch: AppDispatch = useDispatch();

  useEffect(() => {
    getData();
  }, []);

  const getData = async () => {
    const result = await productDA.getProductBestSeller();

    if (result) {
      setData(result.data);
    }
    setLoading(false);
  };

  return (
    <View
      style={{
        height: props.horizontal ? 460 : undefined,
      }}>
      <FDialog ref={dialogRef} />
      {props.titleList ? (
        <View
          style={{
            alignItems: 'center',
            justifyContent: 'space-between',
            paddingHorizontal: 16,
            flexDirection: 'row',
            backgroundColor:
              ColorThemes.light.neutral_absolute_background_color,
          }}>
          <Text
            style={{
              ...TypoSkin.heading5,
              color: ColorThemes.light.neutral_text_title_color,
            }}>
            {props.titleList}
          </Text>
          {props.isSeeMore ? (
            <AppButton
              title={'Xem thêm'}
              containerStyle={{
                justifyContent: 'flex-start',
                alignSelf: 'baseline',
              }}
              backgroundColor={'transparent'}
              textStyle={{
                ...TypoSkin.buttonText3,
                color: ColorThemes.light.infor_main_color,
              }}
              borderColor="transparent"
              suffixIconSize={16}
              suffixIcon={'outline/arrows/circle-arrow-right'}
              onPress={props.onPressSeeMore}
              textColor={ColorThemes.light.infor_main_color}
            />
          ) : null}
        </View>
      ) : null}
      {data ? (
        <ProductCarousel
          title={'Sản phẩm bán chạy'}
          // title={props.titleList}
          products={data}
          onSeeAll={props.onPressSeeMore}
          onProductPress={item => {
            navigation.push(RootScreen.ProductDetail, {id: item.Id});
          }}
          onAddToCart={item => {
            //TODO: add to cart
            dispatch(CartActions.addItemToCart(item, 1));
          }}
          onFavoritePress={item => {
            //TODO: add to favorite
          }}
        />
      ) : (
        <></>
      )}
    </View>
  );
}
