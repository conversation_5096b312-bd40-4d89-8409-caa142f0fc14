/* eslint-disable react/no-unstable-nested-components */
/* eslint-disable react-native/no-inline-styles */
/* eslint-disable react-hooks/exhaustive-deps */
import React, {forwardRef, useEffect, useRef, useState} from 'react';
import {
  View,
  Text,
  FlatList,
  ActivityIndicator,
  Dimensions,
  SafeAreaView,
  TouchableOpacity,
  ScrollView,
  Pressable,
  KeyboardAvoidingView,
  Platform,
} from 'react-native';
import {
  AppButton,
  FBottomSheet,
  FDialog,
  hideBottomSheet,
  showBottomSheet,
  Winicon,
} from 'wini-mobile-components';
import {DefaultNew} from '../card/defaultNew';
import {TextFieldForm} from '../form/component-form';
import {useForm} from 'react-hook-form';
import {useNavigation, useRoute} from '@react-navigation/native';
import {useDispatch, useSelector} from 'react-redux';
import {ColorThemes} from '../../../assets/skin/colors';
import {TypoSkin} from '../../../assets/skin/typography';
import {StorageContanst} from '../../../Config/Contanst';
import {onShare} from '../../../features/share';
import {AppDispatch, RootState} from '../../../redux/store/store';
import {RootScreen} from '../../../router/router';
import EmptyPage from '../../../Screen/emptyPage';
import ScreenHeader from '../../../Screen/Layout/header';
import {dialogCheckAcc} from '../../../Screen/Layout/mainLayout';
import {getDataToAsyncStorage} from '../../../utils/AsyncStorage';
import {SocialDA} from '../da';
import SkeletonPlaceholder from 'react-native-skeleton-placeholder';
import {fetchNews} from '../../../redux/actions/newsAction';

interface Props {
  horizontal?: boolean;
  titleList?: string;
  isSeeMore?: boolean;
  id: string;
  onPressSeeMore?: () => void;
  onRefresh?: () => void;
}

export default function ByNewTrending(props: Props) {
  const navigation = useNavigation<any>();
  const popupRef = useRef<any>(null);
  const dialogRef = useRef<any>(null);
  const dispatch = useDispatch<any>();
  const route = useRoute();
  const [page, setPage] = useState(1);
  const size = 20;
  const {data, loading, totalCount, loadingMore} = useSelector(
    (state: RootState) => state.news,
  );
  // Load news feed data
  useEffect(() => {
    dispatch(fetchNews());
  }, [page]);

  return (
    <View style={{width: '100%', height: props.horizontal ? 386 : undefined}}>
      <FBottomSheet ref={popupRef} />
      <FDialog ref={dialogRef} />
      {props.titleList ? (
        <View
          style={{
            alignItems: 'center',
            justifyContent: 'space-between',
            paddingHorizontal: 16,
            paddingBottom: 16,
            flexDirection: 'row',
            backgroundColor:
              ColorThemes.light.neutral_absolute_background_color,
          }}>
          <Text
            style={{
              ...TypoSkin.heading5,
              color: ColorThemes.light.neutral_text_title_color,
            }}>
            {props.titleList}
          </Text>
          {props.isSeeMore ? (
            <AppButton
              title={'See more'}
              containerStyle={{
                justifyContent: 'flex-start',
                alignSelf: 'baseline',
              }}
              backgroundColor={'transparent'}
              textStyle={TypoSkin.buttonText3}
              borderColor="transparent"
              suffixIconSize={16}
              suffixIcon={'outline/arrows/circle-arrow-right'}
              onPress={() => {
                navigation.navigate(RootScreen.NewsScreen);
              }}
              textColor={ColorThemes.light.infor_main_color}
            />
          ) : null}
        </View>
      ) : null}
      <FlatList
        data={data}
        nestedScrollEnabled
        showsHorizontalScrollIndicator={false}
        showsVerticalScrollIndicator={false}
        contentContainerStyle={{
          gap: 16,
          backgroundColor: ColorThemes.light.neutral_absolute_background_color,
        }}
        keyExtractor={(item, index) => `${item.Id || ''}-${index}`}
        renderItem={({item, index}) => {
          return (
            <DefaultNew
              flexDirection="row"
              onPressDetail={() => {
                navigation.navigate(RootScreen.DetailPost, {
                  item: item,
                });
              }}
              mainContainerStyle={{
                flexDirection: 'row-reverse',
                alignItems: 'flex-start',
              }}
              containerStyle={{
                paddingHorizontal: 16,
              }}
              data={item}
              // actionView={
              //   <View
              //     style={{
              //       flex: 1,
              //       flexDirection: 'row',
              //       alignItems: 'center',
              //     }}>
              //     <View
              //       style={{
              //         flex: 1,
              //         flexDirection: 'row',
              //         gap: 4,
              //         justifyContent: 'flex-start',
              //         alignItems: 'center',
              //       }}>
              //       <AppButton
              //         backgroundColor={'transparent'}
              //         borderColor="transparent"
              //         onPress={async () => {
              //           var token = await getDataToAsyncStorage(
              //             StorageContanst.accessToken,
              //           );
              //           if (token) {
              //             // await dispatch(
              //             //   newsFeedActions.updateLike(
              //             //     item.Id,
              //             //     item.IsLike === true,
              //             //   ),
              //             // );
              //           } else {
              //             ///TODO: check chưa login thì confirm ra trang login
              //             dialogCheckAcc(dialogRef);
              //           }
              //         }}
              //         containerStyle={{padding: 4}}
              //         title={`${item.Likes ?? 0}`}
              //         textColor={
              //           item.IsLike === true
              //             ? ColorThemes.light.primary_main_color
              //             : ColorThemes.light.neutral_text_subtitle_color
              //         }
              //         textStyle={TypoSkin.buttonText6}
              //         prefixIconSize={16}
              //         prefixIcon={
              //           item.IsLike === true
              //             ? 'fill/user interface/like'
              //             : 'outline/user interface/like'
              //         }
              //       />
              //       <AppButton
              //         backgroundColor={'transparent'}
              //         borderColor="transparent"
              //         onPress={async () => {
              //           var token = await getDataToAsyncStorage(
              //             StorageContanst.accessToken,
              //           );
              //           if (token) {
              //             showBottomSheet({
              //               ref: popupRef,
              //               enableDismiss: true,
              //               children: (
              //                 <BottomSheetComments item={item} ref={popupRef} />
              //               ),
              //             });
              //           } else {
              //             dialogCheckAcc(dialogRef);
              //           }
              //         }}
              //         containerStyle={{padding: 4}}
              //         title={`${item.Comment ?? 0}`}
              //         textColor={ColorThemes.light.neutral_text_subtitle_color}
              //         textStyle={TypoSkin.buttonText6}
              //         prefixIconSize={16}
              //         prefixIcon={'outline/user interface/b-comment'}
              //       />
              //     </View>
              //     <View
              //       style={{
              //         flex: 1,
              //         flexDirection: 'row',
              //         justifyContent: 'flex-end',
              //         alignItems: 'center',
              //         gap: 8,
              //       }}>
              //       <AppButton
              //         backgroundColor={'transparent'}
              //         borderColor="transparent"
              //         onPress={async () => {
              //           var token = await getDataToAsyncStorage(
              //             StorageContanst.accessToken,
              //           );
              //           if (token) {
              //             await dispatch();
              //             // newsFeedActions.addBookmark(
              //             //   item.Id,
              //             //   item.IsBookmark === true,
              //             // ),
              //           } else {
              //             ///TODO: check chưa login thì confirm ra trang login
              //             dialogCheckAcc(dialogRef);
              //           }
              //         }}
              //         containerStyle={{padding: 4}}
              //         title={
              //           <Winicon
              //             src={
              //               item.IsBookmark === true
              //                 ? 'fill/user interface/bookmark'
              //                 : 'outline/user interface/bookmark'
              //             }
              //             size={16}
              //             color={
              //               item.IsBookmark === true
              //                 ? ColorThemes.light.warning_main_color
              //                 : ColorThemes.light.neutral_text_subtitle_color
              //             }
              //           />
              //         }
              //       />
              //       <AppButton
              //         backgroundColor={'transparent'}
              //         borderColor="transparent"
              //         onPress={() => {
              //           onShare({content: 'Hello world'});
              //         }}
              //         containerStyle={{padding: 4}}
              //         title={
              //           <Winicon src="fill/arrows/social-sharing" size={16} />
              //         }
              //       />
              //     </View>
              //   </View>
              // }
            />
          );
        }}
        style={{width: '100%', height: '100%'}}
        horizontal={props.horizontal}
        onEndReachedThreshold={0.5}
        initialNumToRender={size}
        ListEmptyComponent={() => {
          if (loading) {
            // danh sách skeletonNewCard trong array 3 phần tử
            return Array(3).map((item, index) => (
              <SkeletonNewCard key={index} />
            ));
          }
          return <EmptyPage />;
        }}
      />
    </View>
  );
}

const BottomSheetComments = forwardRef(function BottomSheetComments(
  data: {
    item: any;
  },
  ref: any,
) {
  const dispatch: AppDispatch = useDispatch();
  const {item} = data;
  const methods = useForm<any>({
    shouldFocusError: false,
  });
  return (
    <Pressable
      style={{
        width: '100%',
        height: Dimensions.get('window').height - 65,
        borderTopLeftRadius: 16,
        borderTopRightRadius: 16,
        backgroundColor: ColorThemes.light.neutral_absolute_background_color,
      }}>
      <ScreenHeader
        style={{
          backgroundColor: ColorThemes.light.transparent,
          paddingVertical: 4,
        }}
        title={`Bình luận`}
        prefix={<View />}
        action={
          <TouchableOpacity
            onPress={() => hideBottomSheet(ref)}
            style={{padding: 12, alignItems: 'center'}}>
            <Winicon
              src="outline/layout/xmark"
              size={20}
              color={ColorThemes.light.neutral_text_body_color}
            />
          </TouchableOpacity>
        }
      />
      <KeyboardAvoidingView
        style={{flex: 1}}
        behavior={Platform.OS === 'ios' ? 'padding' : 'height'} // Adjust for iOS and Android
        keyboardVerticalOffset={Platform.OS === 'ios' ? 65 : 0} // Offset for iOS
      >
        <ScrollView nestedScrollEnabled style={{flex: 1, height: '100%'}}>
          <View style={{paddingHorizontal: 16}}>
            <DefaultNew
              mainContainerStyle={{
                alignItems: 'center',
              }}
              data={{
                Img: item.Img,
                Name: item.Title,
                Id: item.Id,
                relativeUser: item?.relativeUser,
                Content: undefined,
                Description: undefined,
              }}
            />
            <View
              style={{
                flex: 1,
                flexDirection: 'row',
                gap: 4,
                justifyContent: 'flex-start',
                alignItems: 'center',
                borderBottomColor:
                  ColorThemes.light.neutral_bolder_border_color,
                borderBottomWidth: 0.5,
              }}>
              <AppButton
                backgroundColor={'transparent'}
                borderColor="transparent"
                onPress={async () => {
                  var token = await getDataToAsyncStorage(
                    StorageContanst.accessToken,
                  );
                  if (token) {
                    // await dispatch(
                    //   newsFeedActions.updateLike(item.Id, item.IsLike === true),
                    // );
                  } else {
                    ///TODO: check chưa login thì confirm ra trang login
                  }
                }}
                containerStyle={{padding: 4}}
                title={`${item.Likes ?? 0}`}
                textColor={
                  item.IsLike
                    ? ColorThemes.light.primary_main_color
                    : ColorThemes.light.neutral_text_subtitle_color
                }
                textStyle={TypoSkin.buttonText6}
                prefixIconSize={16}
                prefixIcon={
                  item.IsLike
                    ? 'fill/user interface/like'
                    : 'outline/user interface/like'
                }
              />
              <AppButton
                backgroundColor={'transparent'}
                borderColor="transparent"
                onPress={undefined}
                containerStyle={{padding: 4}}
                title={`${item.Comment ?? 0}`}
                textColor={ColorThemes.light.neutral_text_subtitle_color}
                textStyle={TypoSkin.buttonText6}
                prefixIconSize={16}
                prefixIcon={'outline/user interface/b-comment'}
              />
            </View>
          </View>
          <Pressable style={{flex: 1}}>
            {/* <CommentsListNews Id={item.Id} /> */}
          </Pressable>
        </ScrollView>
        <View
          style={{
            flexDirection: 'row',
            gap: 8,
            paddingHorizontal: 16,
            paddingVertical: 16,
            alignItems: 'center',
            justifyContent: 'space-between',
          }}>
          <TextFieldForm
            textFieldStyle={{padding: 16}}
            style={{flex: 1}}
            register={methods.register}
            control={methods.control}
            errors={methods.formState.errors}
            placeholder="Write a comment"
            name="Comment"
          />
          <AppButton
            title={'Send'}
            backgroundColor={ColorThemes.light.primary_main_color}
            borderColor="transparent"
            containerStyle={{
              borderRadius: 8,
              paddingHorizontal: 12,
              height: 45,
            }}
            onPress={async () => {
              var token = await getDataToAsyncStorage(
                StorageContanst.accessToken,
              );
              if (token) {
                if (methods.getValues().Comment) {
                  // await dispatch(
                  //   newsFeedActions.addComment(
                  //     item.Id,
                  //     methods.getValues().Comment,
                  //   ),
                  // );
                  methods.reset();
                }
              } else {
                ///TODO: check chưa login thì confirm ra trang login
              }
            }}
            textColor={ColorThemes.light.neutral_absolute_background_color}
          />
        </View>
      </KeyboardAvoidingView>
    </Pressable>
  );
});

const SkeletonNewCard = () => {
  return (
    <SkeletonPlaceholder>
      <SkeletonPlaceholder.Item
        width="100%"
        height={120}
        borderRadius={8}
        marginBottom={16}>
        <SkeletonPlaceholder.Item
          width="60%"
          height={16}
          borderRadius={4}
          marginBottom={6}
          backgroundColor="#e0e0e0"
        />
        <SkeletonPlaceholder.Item
          width="40%"
          height={12}
          borderRadius={4}
          backgroundColor="#e0e0e0"
        />
      </SkeletonPlaceholder.Item>
    </SkeletonPlaceholder>
  );
};
