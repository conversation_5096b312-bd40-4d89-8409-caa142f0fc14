import React, {useEffect, useMemo, useState} from 'react';
import {View, Text, StyleSheet, TouchableOpacity} from 'react-native';
import LinearGradient from 'react-native-linear-gradient';
import {DataController} from '../../../base/baseController';
import {Winicon} from 'wini-mobile-components';
import {ColorThemes} from '../../../assets/skin/colors';
import {TypoSkin} from '../../../assets/skin/typography';
import {useSelectorCustomerState} from '../../../redux/hook/customerHook';
import {MissionType} from '../../../Config/Contanst';
import {navigate, RootScreen} from '../../../router/router';

export default function Missions() {
  const [showDailyTasks, setShowDailyTasks] = useState(true);
  const [showMonthlyTasks, setShowMonthlyTasks] = useState(true);

  const [missions, setMissions] = useState<any[]>([]); // State to store missions data
  const [dailyMissions, setDailyMissions] = useState<any[]>([]);
  const [monthlyMissions, setMonthlyMissions] = useState<any[]>([]);
  // loading
  const [loading, setLoading] = useState(true);
  const missionController = new DataController('Mission');
  const missionCustomerController = new DataController('MissionCustomer');
  const customer = useSelectorCustomerState().data;

  useEffect(() => {
    const fetchData = async () => {
      const response = await missionController.getAll();
      if (response.code === 200) {
        // Lấy danh sách các mission của customer trong hôm nay từ 0h00 đến 23h59 theo timestamp
        const startOfDay = new Date().setHours(0, 0, 0, 0);
        const endOfDay = new Date().setHours(23, 59, 59, 999);
        const missionCustomer = await missionCustomerController.getListSimple({
          query: `@CustomerId: {${customer?.Id}} @DateCreated: [${startOfDay} ${endOfDay}]`,
        });

        if (missionCustomer.code === 200 && missionCustomer.data.length > 0) {
          response.data = response.data.map((mission: any) => {
            if (mission.Id === missionCustomer.data[0].MissionId) {
              mission.completed = true;
            }
            return {...mission, completed: mission.completed || false};
          });
        }
        const missions = response.data;
        missions.map((mission: any) => {
          mission.reward = mission.Value;
          mission.id = mission.Id;
          return mission;
        });
        setMissions(response.data);

        // Separate missions by Type: 1 = daily, 2 = monthly
        const daily = response.data.filter(
          (mission: any) => mission.Type === 1,
        );
        const monthly = response.data.filter(
          (mission: any) => mission.Type === 2,
        );

        setDailyMissions(daily);
        setMonthlyMissions(monthly);
        setLoading(false);
      } else {
        console.error('Error fetching missions data:', response.message);
        setLoading(false);
      }
    };
    fetchData();
  }, []);

  const completedDailyMissions = useMemo(
    () => dailyMissions.filter((mission: any) => mission.completed).length,
    [dailyMissions],
  );

  const completedMonthlyMissions = useMemo(
    () => monthlyMissions.filter((mission: any) => mission.completed).length,
    [monthlyMissions],
  );

  return (
    <View
      style={{
        marginHorizontal: 16,
        marginTop: 16,
      }}>
      <LinearGradient
        colors={['#90C8FB', '#8DC4F7E5', '#B6F5FE']}
        start={{x: 0, y: 0.5}}
        end={{x: 1, y: 0.5}}
        style={{
          flex: 1,
          borderRadius: 15,
        }}>
        <View style={styles.container}>
          <TouchableOpacity
            style={styles.titleRow}
            onPress={() => setShowDailyTasks(prev => !prev)}
            activeOpacity={0.7}>
            <Text style={styles.title}>Nhiệm vụ ngày</Text>
            <View style={{flexDirection: 'row', alignItems: 'center', gap: 4}}>
              <Text style={styles.progressText}>
                {completedDailyMissions}/{dailyMissions.length}
              </Text>
              <Winicon
                src={
                  showDailyTasks
                    ? 'outline/arrows/down-arrow'
                    : 'outline/arrows/right-arrow'
                }
                size={12}
                color={ColorThemes.light.primary_main_color}
              />
            </View>
          </TouchableOpacity>
          {showDailyTasks && !loading && (
            <>
              <View style={styles.progressBarBackground}>
                <View
                  style={[
                    styles.progressBarFill,
                    {
                      width: `${
                        (completedDailyMissions / dailyMissions.length) * 100
                      }%`,
                    },
                  ]}
                />
              </View>
              {dailyMissions &&
                dailyMissions.map((task: any) => (
                  <TouchableOpacity
                    activeOpacity={0.7}
                    onPress={() => {
                      if (!task.completed) {
                        // TODO: handle complete mission
                        if (
                          task.MissonType === MissionType.Like ||
                          task.MissonType === MissionType.Share
                        ) {
                          navigate(RootScreen.navigateEComView, {
                            screen: 'news',
                          });
                        } else if (task.MissonType === MissionType.Order) {
                          navigate(RootScreen.navigateEComView, {
                            screen: 'products',
                          });
                        }
                      }
                    }}
                    key={task.id}
                    style={styles.taskRow}>
                    <Winicon
                      src={
                        task.completed
                          ? 'fill/layout/circle-check'
                          : 'outline/layout/circle-check'
                      }
                      size={16}
                      color={
                        task.completed
                          ? ColorThemes.light.success_main_color
                          : ColorThemes.light.neutral_text_title_color
                      }
                    />
                    <Text style={styles.taskText}>{task?.Name || ''}</Text>
                    <View
                      style={
                        task.completed
                          ? styles.rewardBox
                          : styles.rewardBoxDisabled
                      }>
                      <Text
                        style={
                          task.completed
                            ? styles.rewardText
                            : styles.rewardTextDisabled
                        }>
                        +{task?.reward || 0} $
                      </Text>
                    </View>
                  </TouchableOpacity>
                ))}
            </>
          )}
        </View>
        <View style={{flex: 1, paddingHorizontal: 16, paddingBottom: 16}}>
          <TouchableOpacity
            style={styles.titleRow}
            onPress={() => setShowMonthlyTasks(prev => !prev)}
            activeOpacity={0.7}>
            <Text style={styles.title}>Nhiệm vụ tháng</Text>
            <View style={{flexDirection: 'row', alignItems: 'center', gap: 4}}>
              <Text style={styles.progressText}>
                {completedMonthlyMissions}/{monthlyMissions.length}
              </Text>
              <Winicon
                src={
                  showMonthlyTasks
                    ? 'outline/arrows/down-arrow'
                    : 'outline/arrows/right-arrow'
                }
                size={12}
                color={ColorThemes.light.primary_main_color}
              />
            </View>
          </TouchableOpacity>
          {showMonthlyTasks && !loading && (
            <>
              <View style={styles.progressBarBackground}>
                <View
                  style={[
                    styles.progressBarFill,
                    {
                      width: `${
                        (completedMonthlyMissions / monthlyMissions.length) *
                        100
                      }%`,
                    },
                  ]}
                />
              </View>
              {monthlyMissions &&
                monthlyMissions.map((task: any) => (
                  <TouchableOpacity
                    activeOpacity={0.7}
                    onPress={() => {
                      if (!task.completed) {
                        // TODO: handle complete mission
                        if (
                          task.MissonType === MissionType.Like ||
                          task.MissonType === MissionType.Share
                        ) {
                          navigate(RootScreen.navigateEComView, {
                            screen: 'news',
                          });
                        } else if (task.MissonType === MissionType.Order) {
                          navigate(RootScreen.navigateEComView, {
                            screen: 'products',
                          });
                        }
                      }
                    }}
                    key={task.id}
                    style={styles.taskRow}>
                    <Winicon
                      src={
                        task.completed
                          ? 'fill/layout/circle-check'
                          : 'outline/layout/circle-check'
                      }
                      size={16}
                      color={
                        task.completed
                          ? ColorThemes.light.success_main_color
                          : ColorThemes.light.neutral_text_title_color
                      }
                    />
                    <Text style={styles.taskText}>{task?.Name || ''}</Text>
                    <View
                      style={
                        task.completed
                          ? styles.rewardBox
                          : styles.rewardBoxDisabled
                      }>
                      <Text
                        style={
                          task.completed
                            ? styles.rewardText
                            : styles.rewardTextDisabled
                        }>
                        +{task?.reward || 0} $
                      </Text>
                    </View>
                  </TouchableOpacity>
                ))}
            </>
          )}
        </View>
      </LinearGradient>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    padding: 16,
  },
  titleRow: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
  },
  title: {
    ...TypoSkin.title3,
  },
  progressBarBackground: {
    height: 8,
    backgroundColor: '#e0e0e0',
    borderRadius: 4,
    marginVertical: 12,
    width: '100%',
  },
  progressBarFill: {
    height: 8,
    backgroundColor: ColorThemes.light.primary_main_color,
    borderRadius: 4,
  },
  progressText: {
    ...TypoSkin.body3,
    color: ColorThemes.light.primary_main_color,
  },
  taskRow: {
    flexDirection: 'row',
    alignItems: 'center',
    marginVertical: 4,
    gap: 8,
  },

  taskText: {
    flex: 1,
    ...TypoSkin.buttonText3,
  },
  rewardBox: {
    backgroundColor: ColorThemes.light.success_main_color,
    borderRadius: 6,
    paddingHorizontal: 12,
    paddingVertical: 4,
    marginLeft: 8,
  },
  rewardBoxDisabled: {
    backgroundColor: '#cccccc',
    borderRadius: 6,
    paddingHorizontal: 12,
    paddingVertical: 4,
    marginLeft: 8,
  },
  rewardText: {
    color: ColorThemes.light.neutral_absolute_background_color,
    fontWeight: 'bold',
  },
  rewardTextDisabled: {
    color: ColorThemes.light.neutral_absolute_background_color,

    fontWeight: 'bold',
  },
});
