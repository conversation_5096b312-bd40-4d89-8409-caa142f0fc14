import {createAsyncThunk} from '@reduxjs/toolkit';
import {showSnackbar, ComponentStatus} from 'wini-mobile-components';
import {DataController} from '../../base/baseController';
import {RootState} from '../store/store';
import {FavoriteProduct} from '../models/favoriteProduct';
import {getProduct} from './productAction';

const getFavoriteProducts = async (
  config: any,
  thunkAPI: any,
): Promise<{data: FavoriteProduct[]; totalCount: number}> => {
  const controller = new DataController('ProductFavorite');
  const customerId = thunkAPI.getState().customer.data?.Id;
  const res = await controller.aggregateList({
    page: config?.page ?? 1,
    size: config?.size ?? 2,
    searchRaw: `@CustomerId:{${customerId}} ${config?.search ?? ''}`,
    sortby: [{prop: 'DateCreated', direction: 'DESC'}],
  });
  if (res.code === 200) {
    const data = res.data;
    const listProduct = await getProduct(
      data.map((item: any) => item.ProductId),
    );
    data.forEach((item: any) => {
      item.Product = listProduct.find(
        (product: any) => product.Id === item.ProductId,
      );
    });
    return {data, totalCount: res.totalCount};
  }
  return {data: [], totalCount: 0};
};

const deleteFavoriteProduct = async (ids: string[]) => {
  const controller = new DataController('ProductFavorite');
  const res = await controller.delete(ids);
  return res;
};

const fetchFavoriteProducts = createAsyncThunk<
  {data: FavoriteProduct[]; totalCount: number},
  {page?: number; size?: number; search?: string} | undefined,
  {state: RootState}
>(
  'favoriteProduct/fetchFavoriteProducts',
  async (config: any, thunkAPI: any) => {
    try {
      const data = await getFavoriteProducts(config, thunkAPI);
      return {data: data.data, totalCount: data.totalCount};
    } catch (err: any) {
      const errorMessage = err.message || 'An unknown error occurred';
      showSnackbar({message: errorMessage, status: ComponentStatus.ERROR});
      return {data: [], totalCount: 0};
    }
  },
);

const loadmoreFavoriteProducts = createAsyncThunk<
  FavoriteProduct[],
  {page?: number; size?: number; search?: string} | undefined,
  {state: RootState}
>(
  'favoriteProduct/loadmoreFavoriteProducts',
  async (config: any, thunkAPI: any): Promise<FavoriteProduct[]> => {
    try {
      const data = await getFavoriteProducts(config, thunkAPI);
      if (!data) {
        return [];
      }
      return data.data;
    } catch (err: any) {
      const errorMessage = err.message || 'An unknown error occurred';
      showSnackbar({message: errorMessage, status: ComponentStatus.ERROR});
      return [];
    }
  },
);

const unFavoriteProduct = createAsyncThunk<any, {id: string} | undefined>(
  'favoriteProduct/unFavoriteProduct',
  async (config: any) => {
    try {
      await deleteFavoriteProduct([config.id]);
      return config.id;
    } catch (err: any) {
      const errorMessage = err.message || 'An unknown error occurred';
      showSnackbar({message: errorMessage, status: ComponentStatus.ERROR});
      return config.id;
    }
  },
);

export {fetchFavoriteProducts, loadmoreFavoriteProducts, unFavoriteProduct};
