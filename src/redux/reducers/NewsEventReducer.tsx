import {PayloadAction, createSlice} from '@reduxjs/toolkit';
import {NewsEvent} from '../models/newsEvent';
import {fetchNewsEvents, loadMoreNewsEvent} from '../actions/newEventAction';

interface EventStoreState {
  data: Array<NewsEvent>;
  totalCount: number;
  page: number;
  loading?: boolean;
  loadingMore?: boolean;

  type?: string;
}

export type {EventStoreState};

const initState: EventStoreState = {
  data: [],
  totalCount: 0,
  page: 0,
  loading: false,
  loadingMore: false,
};

export const newsEventSlice = createSlice({
  name: 'newsEvent',
  initialState: initState,
  reducers: {
    setData: <K extends keyof EventStoreState>(
      state: EventStoreState,
      action: PayloadAction<{
        stateName: K;
        data: EventStoreState[K];
      }>,
    ) => {
      state[action.payload.stateName] = action.payload.data;
    },
  },
  extraReducers: builder => {
    builder
      .addCase(fetchNewsEvents.pending, state => {
        state.loading = true;
      })
      .addCase(
        fetchNewsEvents.fulfilled,
        (state, action: PayloadAction<any>) => {
          state.loading = false;
          state.data = action.payload.data;
          state.totalCount = action.payload.totalCount;
        },
      )
      .addCase(fetchNewsEvents.rejected, state => {
        state.loading = false;
      })
      .addCase(loadMoreNewsEvent.pending, state => {
        state.loadingMore = true;
      })
      .addCase(
        loadMoreNewsEvent.fulfilled,
        (state, action: PayloadAction<NewsEvent[]>) => {
          state.loadingMore = false;
          state.data = [...state.data, ...action.payload];
        },
      );
  },
});

export const {setData} = newsEventSlice.actions;

export default newsEventSlice.reducer;
